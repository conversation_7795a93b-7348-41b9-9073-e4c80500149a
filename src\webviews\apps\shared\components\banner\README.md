# GitLens Banner Component

A reusable banner component built with LitElement for displaying promotional content, notifications, and call-to-action messages in GitLens webviews.

## Features

- **Multiple Display Modes**: solid, outline, gradient, gradient-transparent
- **Flexible Content**: Optional title, body text, and buttons
- **Theme Integration**: Automatically adapts to VSCode theme colors
- **Button Integration**: Uses existing `gl-button` component
- **Event System**: Custom events for button interactions
- **Responsive Design**: Adapts to different screen sizes

## Basic Usage

```typescript
import '../../shared/components/banner/banner';

// In your LitElement render method:
html`
  <gl-banner
    display="gradient"
    banner-title="Welcome to GitLens Pro"
    body="Unlock advanced features and boost your productivity."
    primary-button="Upgrade Now"
    secondary-button="Learn More"
    @gl-banner-primary-click=${this.onUpgrade}
    @gl-banner-secondary-click=${this.onLearnMore}
  ></gl-banner>
`;
```

## Display Modes

### Solid (default)
Uses the same background as card components with subtle border.
```html
<gl-banner display="solid" banner-title="Notification" body="Message content"></gl-banner>
```

### Outline
Emphasis color outline with secondary background.
```html
<gl-banner display="outline" banner-title="Feature Update" body="New features available"></gl-banner>
```

### Gradient
Horizontal gradient from primary to secondary emphasis colors.
```html
<gl-banner display="gradient" banner-title="Special Offer" body="Limited time promotion"></gl-banner>
```

### Gradient Transparent
Same gradient but with 50% transparency.
```html
<gl-banner display="gradient-transparent" banner-title="Integration" body="Connect your accounts"></gl-banner>
```

## Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `display` | `BannerDisplay` | `'solid'` | Visual display mode |
| `banner-title` | `string` | `undefined` | Bold title text at top |
| `body` | `string` | `undefined` | Body text in center |
| `primary-button` | `string` | `undefined` | Primary button text |
| `primary-button-href` | `string` | `undefined` | Primary button link |
| `primary-button-command` | `string` | `undefined` | Primary button command |
| `secondary-button` | `string` | `undefined` | Secondary button text |
| `secondary-button-href` | `string` | `undefined` | Secondary button link |
| `secondary-button-command` | `string` | `undefined` | Secondary button command |

## Events

### gl-banner-primary-click
Fired when the primary button is clicked.
```typescript
detail: { command?: string }
```

### gl-banner-secondary-click
Fired when the secondary button is clicked.
```typescript
detail: { command?: string }
```

## CSS Custom Properties

The banner component uses CSS custom properties for theming:

```css
:host {
  /* Color properties */
  --gl-banner-primary-background: var(--vscode-sideBar-background);
  --gl-banner-secondary-background: var(--vscode-editor-background);
  --gl-banner-primary-emphasis-background: var(--vscode-button-background);
  --gl-banner-secondary-emphasis-background: var(--vscode-button-secondaryBackground);
  --gl-banner-text-color: var(--vscode-foreground);
  --gl-banner-dim-text-color: var(--vscode-descriptionForeground);
  --gl-banner-transparency: 0.5;

  /* Layout properties */
  --gl-banner-padding: 1.2rem;
  --gl-banner-gap: 0.8rem;
  --gl-banner-border-radius: 0.4rem;
}
```

## Promo Banner Integration

For home view promo banners that integrate with the promo system:

```typescript
import './components/promo-banner-enhanced';

// In home.ts render method:
html`
  <gl-promo-banner-enhanced
    display="gradient"
    section-key="myPromoBanner"
    banner-title="Custom Title"
    primary-button-text="Get Started"
    secondary-button-text="Dismiss"
  ></gl-promo-banner-enhanced>
`;
```

The enhanced promo banner automatically:
- Fetches applicable promos from the promo system
- Handles dismissal with persistence
- Integrates with VSCode command system
- Manages visibility based on promo availability

## Integration Example

To add a promo banner to the home view above the active work section:

1. Import the component:
```typescript
import './components/promo-banner-enhanced';
```

2. Add to render method:
```typescript
${when(this.state?.previewEnabled === true, () => html`
  <gl-preview-banner></gl-preview-banner>
  <gl-promo-banner-enhanced
    display="gradient"
    section-key="homePromoBanner"
  ></gl-promo-banner-enhanced>
  <gl-active-work></gl-active-work>
  <gl-launchpad></gl-launchpad>
  <gl-overview></gl-overview>
`)}
```

This will display the promo banner between the preview banner and active work section, with automatic promo content and dismissal functionality.
